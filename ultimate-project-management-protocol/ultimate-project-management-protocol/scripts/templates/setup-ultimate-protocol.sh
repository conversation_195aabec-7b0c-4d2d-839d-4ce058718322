#!/bin/bash
# 🚀 Universal Ultimate Project Management Protocol Setup
# Use this script to set up the protocol on ANY project

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

PROJECT_NAME=$(basename "$(pwd)")

echo -e "${BLUE}🚀 ULTIMATE PROJECT MANAGEMENT PROTOCOL SETUP${NC}"
echo -e "${CYAN}Setting up for project: ${PROJECT_NAME}${NC}"
echo "=================================================="

# Function to detect project type
detect_project_type() {
    local project_type="unknown"
    local framework="unknown"
    local language="unknown"
    
    # Detect language and framework
    if [ -f "package.json" ]; then
        language="javascript"
        if grep -q "typescript" package.json; then
            language="typescript"
        fi
        
        if grep -q "next" package.json; then
            framework="nextjs"
        elif grep -q "react" package.json; then
            framework="react"
        elif grep -q "vue" package.json; then
            framework="vue"
        elif grep -q "express" package.json; then
            framework="express"
        fi
        project_type="web-app"
    elif [ -f "requirements.txt" ] || [ -f "pyproject.toml" ]; then
        language="python"
        if grep -q "django" requirements.txt 2>/dev/null; then
            framework="django"
        elif grep -q "flask" requirements.txt 2>/dev/null; then
            framework="flask"
        elif grep -q "fastapi" requirements.txt 2>/dev/null; then
            framework="fastapi"
        fi
        project_type="web-app"
    elif [ -f "Cargo.toml" ]; then
        language="rust"
        project_type="application"
    elif [ -f "go.mod" ]; then
        language="go"
        project_type="application"
    fi
    
    echo "$project_type,$framework,$language"
}

# Function to create directory structure
create_directory_structure() {
    echo -e "\n${YELLOW}📁 Creating directory structure...${NC}"
    
    # Create core directories
    mkdir -p docs scripts
    
    # Create project-specific directories based on type
    local project_info=$(detect_project_type)
    local project_type=$(echo $project_info | cut -d',' -f1)
    local framework=$(echo $project_info | cut -d',' -f2)
    local language=$(echo $project_info | cut -d',' -f3)
    
    if [ "$language" = "typescript" ] || [ "$language" = "javascript" ]; then
        mkdir -p src __tests__
        if [ "$framework" = "nextjs" ]; then
            mkdir -p src/app src/components src/lib
        elif [ "$framework" = "react" ]; then
            mkdir -p src/components src/utils src/hooks
        fi
    elif [ "$language" = "python" ]; then
        mkdir -p src tests
    fi
    
    echo -e "${GREEN}✅ Directory structure created${NC}"
}

# Function to create project configuration
create_project_config() {
    echo -e "\n${YELLOW}⚙️ Creating project configuration...${NC}"
    
    local project_info=$(detect_project_type)
    local project_type=$(echo $project_info | cut -d',' -f1)
    local framework=$(echo $project_info | cut -d',' -f2)
    local language=$(echo $project_info | cut -d',' -f3)
    
    cat > project-config.yml << EOF
# Universal Project Navigation Configuration
# Auto-generated by Ultimate Project Management Protocol

project:
  name: "$PROJECT_NAME"
  type: "$project_type"
  language: "$language"
  framework: "$framework"
  description: "Auto-detected project configuration"

# CANONICAL DIRECTORY STRUCTURE (SINGLE SOURCE OF TRUTH)
source_structure:
  main_dir: "src"
  components_dir: "src/components"
  utils_dir: "src/lib"
  tests_dir: "__tests__"
  docs_dir: "docs"
  scripts_dir: "scripts"

# DUPLICATE PREVENTION RULES
duplicate_prevention:
  forbidden_locations:
    - "components"          # Use src/components instead
    - "lib"                # Use src/lib instead
    - "utils"              # Use src/utils instead
    - "docs/nested"        # Use root docs/ instead
  
  canonical_mappings:
    components: "src/components"
    utilities: "src/lib"
    documentation: "docs"
    tests: "__tests__"
    scripts: "scripts"

# NAMING CONVENTIONS
naming_conventions:
  files:
    components: "PascalCase"
    utilities: "camelCase"
    tests: "kebab-case.test"
    documentation: "UPPER_CASE"
  
  code:
    functions: "camelCase"
    classes: "PascalCase"
    constants: "UPPER_SNAKE_CASE"
    variables: "camelCase"

# TESTING FRAMEWORK
testing:
  framework: "auto-detect"
  test_pattern: "*.test.*"
  test_location: "__tests__"
  coverage_threshold: 80

# ARCHITECTURE PATTERNS
architecture:
  error_handling: "self-healing"
  validation: "input-output"
  logging: "structured"
  monitoring: "enabled"
EOF
    
    echo -e "${GREEN}✅ Project configuration created${NC}"
}

# Function to copy core protocol files
copy_protocol_files() {
    echo -e "\n${YELLOW}📋 Setting up protocol files...${NC}"
    
    # Copy ultimate discovery script
    cat > scripts/ultimate-project-discovery.sh << 'EOF'
#!/bin/bash
# 🎯 Ultimate Project Discovery Script
# This is a template - customize for your project

echo "🎯 ULTIMATE PROJECT DISCOVERY & ANALYSIS"
echo "=============================================="

# Add your project-specific discovery commands here
find . -type f -name "*.py" -o -name "*.js" -o -name "*.ts" | head -20
find . -name "README*" -o -name "*.md" | head -10

# Check for duplicates
find . -name "components" -type d | grep -v node_modules
find . -name "docs" -type d | grep -v node_modules
find . -name "lib" -type d | grep -v node_modules

echo "✅ Discovery complete"
EOF
    
    # Copy validation script
    cat > scripts/validate-before-create.sh << 'EOF'
#!/bin/bash
# 🛡️ Duplicate Prevention Validation Script

echo "🔍 DUPLICATE PREVENTION CHECK"
echo "=============================="

# Search for duplicates
if [ $# -gt 0 ]; then
    echo "Searching for: $1"
    find . -iname "*$1*" -type f | grep -v node_modules | head -10
    grep -r "$1" --include="*.py" --include="*.js" --include="*.ts" . | head -5
fi

echo "✅ Validation complete"
EOF
    
    # Make scripts executable
    chmod +x scripts/ultimate-project-discovery.sh
    chmod +x scripts/validate-before-create.sh
    
    echo -e "${GREEN}✅ Protocol scripts created${NC}"
}

# Function to create project context template
create_project_context() {
    echo -e "\n${YELLOW}📝 Creating project context document...${NC}"
    
    local project_info=$(detect_project_type)
    local project_type=$(echo $project_info | cut -d',' -f1)
    local framework=$(echo $project_info | cut -d',' -f2)
    local language=$(echo $project_info | cut -d',' -f3)
    
    cat > .project_context.md << EOF
# 🎯 $PROJECT_NAME - Project Context & Intelligence

## 📊 Project Intelligence Summary
- **Type**: $project_type
- **Framework**: $framework
- **Language**: $language
- **Setup Date**: $(date)

## 📁 File Organization Patterns (CANONICAL STRUCTURE)

### **Source Code Structure**
\`\`\`
$PROJECT_NAME/
├── src/                           # CANONICAL: Main source code
├── docs/                          # CANONICAL: Documentation
├── __tests__/                     # CANONICAL: All test files
├── scripts/                       # Utility scripts
└── [config files]                # Root configuration
\`\`\`

## 📝 Naming Conventions

### **Files & Directories**
- **Components**: PascalCase (\`ComponentName.tsx\`)
- **Utilities**: camelCase (\`utilityName.ts\`)
- **Tests**: \`ComponentName.test.tsx\`
- **Documentation**: UPPER_CASE (\`README.md\`)

### **Code Conventions**
- **Functions**: camelCase
- **Classes**: PascalCase
- **Constants**: UPPER_SNAKE_CASE
- **Variables**: camelCase

## 🧰 Existing Core Utilities (DO NOT DUPLICATE)

### **[Add your utilities here as you create them]**
- \`src/lib/utils.ts\` - Common utilities
- \`src/lib/validation.ts\` - Input validation

## 🧪 Testing Framework & Patterns

### **Testing Structure**
- **Framework**: Auto-detected
- **Location**: \`__tests__/\` directory (CANONICAL)
- **Pattern**: Mirror source structure in tests
- **Naming**: \`ComponentName.test.tsx\`

## 🎯 Development Workflow

### **TDD Protocol (MANDATORY)**
1. **Write failing test first**
2. **Implement minimal code to pass**
3. **Refactor with error handling**
4. **Add self-healing mechanisms**
5. **Validate integration**

### **File Creation Checklist**
- [ ] Search for existing similar functionality
- [ ] Verify naming follows project conventions
- [ ] Place in canonical directory structure
- [ ] Write tests first (TDD)
- [ ] Add error handling and validation
- [ ] Update this context document if new patterns emerge

---

**Last Updated**: Auto-generated by Ultimate Project Setup
**Next Review**: Update when adding new architectural patterns
EOF
    
    echo -e "${GREEN}✅ Project context document created${NC}"
}

# Function to create documentation structure
create_documentation_structure() {
    echo -e "\n${YELLOW}📚 Creating documentation structure...${NC}"
    
    # Create documentation directories
    mkdir -p docs/{development,testing,deployment,user-guides}
    
    # Create main documentation files
    cat > docs/README.md << EOF
# 📚 $PROJECT_NAME Documentation

## 📋 Documentation Index

### **Project Management**
- [Project Context](.project_context.md) - Current project intelligence
- [Project Configuration](project-config.yml) - Canonical structure rules

### **Development**
- [Development Guidelines](development/) - Development best practices
- [Testing Strategy](testing/) - Testing framework and patterns

### **Deployment**
- [Deployment Guide](deployment/) - Production deployment instructions

### **User Guides**
- [User Documentation](user-guides/) - End-user documentation

## 🛠️ Quick Start

1. Read the project context: \`.project_context.md\`
2. Run discovery: \`./scripts/ultimate-project-discovery.sh\`
3. Before creating files: \`./scripts/validate-before-create.sh [feature]\`
4. Follow TDD protocol for all new features

## 🎯 Protocol Compliance

This project follows the Ultimate Project Management Protocol for:
- ✅ Zero duplicate files
- ✅ Systematic organization
- ✅ TDD-first development
- ✅ Self-healing architecture
EOF
    
    echo -e "${GREEN}✅ Documentation structure created${NC}"
}

# Function to run initial discovery
run_initial_discovery() {
    echo -e "\n${YELLOW}🔍 Running initial project discovery...${NC}"
    
    ./scripts/ultimate-project-discovery.sh
    
    echo -e "${GREEN}✅ Initial discovery complete${NC}"
}

# Main setup function
main() {
    echo -e "\n${PURPLE}Starting Ultimate Project Management Protocol setup...${NC}"
    
    create_directory_structure
    create_project_config
    copy_protocol_files
    create_project_context
    create_documentation_structure
    run_initial_discovery
    
    echo -e "\n${GREEN}🎉 SETUP COMPLETE!${NC}"
    echo -e "${CYAN}Your project now has:${NC}"
    echo "  ✅ Ultimate project discovery system"
    echo "  ✅ Duplicate prevention protocols"
    echo "  ✅ Project intelligence documentation"
    echo "  ✅ TDD-ready structure"
    echo "  ✅ Self-healing architecture foundation"
    echo ""
    echo -e "${YELLOW}Next steps:${NC}"
    echo "  1. Review .project_context.md"
    echo "  2. Customize project-config.yml if needed"
    echo "  3. Start developing with TDD protocol"
    echo "  4. Use ./scripts/validate-before-create.sh before creating files"
    echo ""
    echo -e "${PURPLE}🚀 Ready for zero-error development!${NC}"
}

# Help function
show_help() {
    echo "Usage: $0"
    echo ""
    echo "Sets up the Ultimate Project Management Protocol for any project."
    echo ""
    echo "This script will:"
    echo "  - Detect your project type and framework"
    echo "  - Create canonical directory structure"
    echo "  - Set up duplicate prevention system"
    echo "  - Create project intelligence documentation"
    echo "  - Install validation and discovery tools"
    echo ""
    echo "Run this script in the root of any project to enable:"
    echo "  ✅ Zero duplicate files"
    echo "  ✅ Systematic organization"
    echo "  ✅ TDD-first development"
    echo "  ✅ Self-healing architecture"
}

# Check for help flag
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main
