import '@testing-library/jest-dom';
import React from 'react';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Mock Next.js link
jest.mock('next/link', () => {
  return function MockedLink({ children, href }: { children: React.ReactNode; href: string }) {
    return React.createElement('a', { href }, children);
  };
});

// Mock NextAuth for tests that don't need real authentication
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      },
    },
    status: 'authenticated',
  }),
  signIn: jest.fn(),
  signOut: jest.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Mock Prisma client for unit tests
// Create comprehensive Prisma mock with all required models
const createMockModel = () => ({
  create: jest.fn(),
  findFirst: jest.fn(),
  findUnique: jest.fn(),
  findMany: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  deleteMany: jest.fn(),
  upsert: jest.fn(),
  count: jest.fn(),
  aggregate: jest.fn(),
  groupBy: jest.fn(),
  createMany: jest.fn(),
  updateMany: jest.fn(),
});

const mockPrisma = {
  user: createMockModel(),
  assessment: createMockModel(),
  assessmentResponse: createMockModel(),
  learningResource: createMockModel(),
  userProgress: createMockModel(),
  resourceRating: createMockModel(),
  forumPost: createMockModel(),
  forumReply: createMockModel(),
  careerPath: createMockModel(),
  careerPathResource: createMockModel(),
  skill: createMockModel(),
  userSkill: createMockModel(),
  notification: createMockModel(),
  userGoal: createMockModel(),
  achievement: createMockModel(),
  userAchievement: createMockModel(),
  postReaction: createMockModel(),
  moderatorAction: createMockModel(),
  report: createMockModel(),
  $connect: jest.fn().mockResolvedValue(undefined),
  $disconnect: jest.fn().mockResolvedValue(undefined),
  $transaction: jest.fn().mockImplementation((callback) => callback(mockPrisma)),
  $executeRaw: jest.fn(),
  $queryRaw: jest.fn(),
};

// Mock the Prisma module
jest.mock('@/lib/prisma', () => ({
  default: mockPrisma,
  __esModule: true,
}));

// Also mock the direct prisma import
jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrisma),
}));

// Export the mock for use in tests
(global as any).mockPrisma = mockPrisma;

// Mock fetch
global.fetch = jest.fn();

// Test environment variables
process.env.NEXTAUTH_SECRET = 'test-secret';
// NODE_ENV is read-only in production builds, so we use a different approach
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test';
}
// Keep the existing DATABASE_URL for real database testing

beforeEach(async () => {
  // Clear all mocks before each test
  jest.clearAllMocks();

  // Reset mock implementations to default state
  Object.values(mockPrisma).forEach(model => {
    if (typeof model === 'object' && model !== null) {
      Object.values(model).forEach(method => {
        if (jest.isMockFunction(method)) {
          method.mockReset();
        }
      });
    }
  });
});

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };

  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('componentWillReceiveProps has been renamed')
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Mock Next.js server runtime objects for testing
class MockNextRequest extends Request {
  constructor(input: RequestInfo, init?: RequestInit) {
    super(input, init);
  }
}

class MockNextResponse extends Response {
  constructor(body?: BodyInit | null, init?: ResponseInit) {
    super(body, init);
  }

  static json(data: unknown, init?: ResponseInit) {
    return new MockNextResponse(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      ...init,
    });
  }

  static redirect(url: string | URL, status: number = 307) {
    return new MockNextResponse(null, { status, headers: { Location: url.toString() } });
  }

  static error() {
    return new MockNextResponse('Internal Server Error', { status: 500 });
  }
}

declare global {
  var NextRequest: typeof MockNextRequest;
  var NextResponse: typeof MockNextResponse;
}

global.NextRequest = MockNextRequest;
global.NextResponse = MockNextResponse; 