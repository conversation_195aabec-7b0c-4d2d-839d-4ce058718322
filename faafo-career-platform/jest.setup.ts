import '@testing-library/jest-dom';
import React from 'react';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Mock Next.js link
jest.mock('next/link', () => {
  return function MockedLink({ children, href }: { children: React.ReactNode; href: string }) {
    return React.createElement('a', { href }, children);
  };
});

// Mock NextAuth for tests that don't need real authentication
jest.mock('next-auth/react', () => ({
  useSession: () => ({
    data: {
      user: {
        id: 'test-user-id',
        email: '<EMAIL>',
        name: 'Test User',
      },
    },
    status: 'authenticated',
  }),
  signIn: jest.fn(),
  signOut: jest.fn(),
  SessionProvider: ({ children }: { children: React.ReactNode }) => children,
}));

// Real Prisma client - no mocking
// Tests will use actual database connections

// Mock fetch
global.fetch = jest.fn();

// Test environment variables
process.env.NEXTAUTH_SECRET = 'test-secret';
// NODE_ENV is read-only in production builds, so we use a different approach
if (!process.env.NODE_ENV) {
  (process.env as any).NODE_ENV = 'test';
}
// Keep the existing DATABASE_URL for real database testing

// Real database setup for testing
import { PrismaClient } from '@prisma/client';

let testPrisma: PrismaClient;

beforeAll(async () => {
  // Initialize real Prisma client for tests
  testPrisma = new PrismaClient();

  // Connect to real database
  await testPrisma.$connect();

  console.log('✅ Connected to real database for testing');
});

afterAll(async () => {
  // Cleanup and disconnect from test database
  if (testPrisma) {
    await testPrisma.$disconnect();
  }
});

beforeEach(async () => {
  // Clear any mocks that still exist
  jest.clearAllMocks();

  // Optional: Clean test data between tests for isolation
  // Uncomment if you want fresh database state for each test
  // await cleanTestData();
});

// Helper function to clean test data (optional)
async function cleanTestData() {
  if (testPrisma) {
    // Clean up test data in reverse order of dependencies
    await testPrisma.forumReply.deleteMany({});
    await testPrisma.forumPost.deleteMany({});
    await testPrisma.assessment.deleteMany({});
    await testPrisma.learningResource.deleteMany({});
    await testPrisma.user.deleteMany({
      where: {
        email: {
          contains: 'test',
        },
      },
    });
  }
}

// Mock console methods to reduce noise in tests
const originalError = console.error;
const originalWarn = console.warn;

beforeAll(() => {
  console.error = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is no longer supported')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };

  console.warn = (...args: any[]) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('componentWillReceiveProps has been renamed')
    ) {
      return;
    }
    originalWarn.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
  console.warn = originalWarn;
});

// Mock Next.js server runtime objects for testing
class MockNextRequest extends Request {
  constructor(input: RequestInfo, init?: RequestInit) {
    super(input, init);
  }
}

class MockNextResponse extends Response {
  constructor(body?: BodyInit | null, init?: ResponseInit) {
    super(body, init);
  }

  static json(data: unknown, init?: ResponseInit) {
    return new MockNextResponse(JSON.stringify(data), {
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      ...init,
    });
  }

  static redirect(url: string | URL, status: number = 307) {
    return new MockNextResponse(null, { status, headers: { Location: url.toString() } });
  }

  static error() {
    return new MockNextResponse('Internal Server Error', { status: 500 });
  }
}

declare global {
  var NextRequest: typeof MockNextRequest;
  var NextResponse: typeof MockNextResponse;
}

global.NextRequest = MockNextRequest;
global.NextResponse = MockNextResponse; 