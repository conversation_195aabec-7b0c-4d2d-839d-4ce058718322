import { NextRequest, NextResponse } from 'next/server';
import * as Sen<PERSON> from "@sentry/nextjs";

// Custom error class for Sentry testing
class SentryExampleAPIError extends Error {
  constructor(message: string | undefined) {
    super(message);
    this.name = "SentryExampleAPIError";
  }
}

// A faulty API route to test Sentry's error monitoring
export async function GET(request: NextRequest) {
  try {
    // Intentionally throw an error for testing
    throw new SentryExampleAPIError("This error is raised on the backend called by the example page.");
  } catch (error) {
    // Capture the error with Sentry
    Sentry.captureException(error);
    
    // Return error response
    return NextResponse.json(
      { error: "Sample error for Sentry testing" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  return GET(request);
}
