import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { ErrorReporter } from '@/lib/errorReporting';
import sharp from 'sharp';
import crypto from 'crypto';

// File upload configuration
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const AVATAR_SIZES = {
  thumbnail: 64,
  small: 128,
  medium: 256,
  large: 512
};

interface ProcessedImage {
  buffer: Buffer;
  size: number;
  format: string;
}

async function processImage(buffer: Buffer, size: number): Promise<ProcessedImage> {
  const processed = await sharp(buffer)
    .resize(size, size, {
      fit: 'cover',
      position: 'center'
    })
    .jpeg({ quality: 85 })
    .toBuffer();

  return {
    buffer: processed,
    size,
    format: 'jpeg'
  };
}

function generateFileName(userId: string, size: string): string {
  const timestamp = Date.now();
  const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);
  return `profile-${userId}-${size}-${hash}.jpg`;
}

// In a real implementation, you would upload to a cloud storage service
// For now, we'll simulate the upload and return a placeholder URL
async function uploadToStorage(buffer: Buffer, fileName: string): Promise<string> {
  // TODO: Implement actual file upload to Vercel Blob or similar service
  // For now, return a placeholder URL
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  return `${baseUrl}/api/profile/photo/${fileName}`;
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, and WebP are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { error: 'File too large. Maximum size is 5MB.' },
        { status: 400 }
      );
    }

    const buffer = Buffer.from(await file.arrayBuffer());

    // Process image for different sizes
    const processedImages = await Promise.all(
      Object.entries(AVATAR_SIZES).map(async ([sizeName, sizePixels]) => {
        const processed = await processImage(buffer, sizePixels);
        const fileName = generateFileName(user.id, sizeName);
        const url = await uploadToStorage(processed.buffer, fileName);
        return { size: sizeName, url, pixels: sizePixels };
      })
    );

    // Use the medium size as the primary profile picture URL
    const primaryImageUrl = processedImages.find(img => img.size === 'medium')?.url;

    if (!primaryImageUrl) {
      throw new Error('Failed to process primary image');
    }

    // Update user profile with new image URL
    const updatedProfile = await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      profilePictureUrl: primaryImageUrl,
      sizes: processedImages.reduce((acc, img) => {
        acc[img.size] = img.url;
        return acc;
      }, {} as Record<string, string>),
      message: 'Profile photo updated successfully'
    });

  } catch (error) {
    console.error('Error uploading profile photo:', error);

    ErrorReporter.captureError(error as Error, {
      userId: session.user.email,
      userEmail: session.user.email,
      action: 'upload_profile_photo',
      component: 'profile_photo_api',
    });

    return NextResponse.json(
      { error: 'Failed to upload profile photo' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Remove profile picture URL from database
    await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      message: 'Profile photo removed successfully'
    });

  } catch (error) {
    console.error('Error removing profile photo:', error);

    ErrorReporter.captureError(error as Error, {
      userId: session.user.email,
      userEmail: session.user.email,
      action: 'remove_profile_photo',
      component: 'profile_photo_api',
    });

    return NextResponse.json(
      { error: 'Failed to remove profile photo' },
      { status: 500 }
    );
  }
}
