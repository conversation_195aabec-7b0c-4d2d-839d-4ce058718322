import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET } from '@/app/api/assessment/results/[id]/route';
import { generateAssessmentInsights } from '@/lib/assessmentScoring';
import { getCareerPathSuggestions } from '@/lib/suggestionService';
import prisma from '@/lib/prisma';

// Mock the auth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(() => Promise.resolve({
    user: { id: 'test-user-id', email: '<EMAIL>' }
  }))
}));

// Mock the auth options
jest.mock('@/lib/auth', () => ({
  authOptions: {}
}));

describe('Assessment Results Integration', () => {
  let testUserId: string;
  let testAssessmentId: string;

  beforeEach(async () => {
    // Create test user
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: new Date(),
      }
    });
    testUserId = testUser.id;

    // Create test assessment
    const testAssessment = await prisma.assessment.create({
      data: {
        userId: testUserId,
        status: 'COMPLETED',
        currentStep: 5,
        completedAt: new Date(),
      }
    });
    testAssessmentId = testAssessment.id;

    // Create test assessment responses
    await prisma.assessmentResponse.createMany({
      data: [
        {
          assessmentId: testAssessmentId,
          questionKey: 'dissatisfaction_triggers',
          answerValue: ['lack_of_growth', 'compensation']
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'financial_comfort',
          answerValue: 4
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'confidence_level',
          answerValue: 3
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'support_system',
          answerValue: 4
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'risk_tolerance',
          answerValue: 3
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'top_skills',
          answerValue: ['technical_programming', 'problem_solving', 'communication']
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'career_change_motivation',
          answerValue: 'better_work_life_balance'
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'biggest_obstacles',
          answerValue: ['financial_concerns', 'lack_of_experience']
        },
        {
          assessmentId: testAssessmentId,
          questionKey: 'transition_timeline',
          answerValue: 'medium_term'
        }
      ]
    });

    // Create test career path
    const testCareerPath = await prisma.careerPath.create({
      data: {
        name: 'Software Developer',
        slug: 'software-developer',
        overview: 'Build software applications and systems',
        pros: JSON.stringify(['High demand', 'Good salary', 'Remote work options']),
        cons: JSON.stringify(['Long hours', 'Constant learning required']),
        actionableSteps: [
          { title: 'Learn programming languages', description: 'Start with Python or JavaScript' },
          { title: 'Build portfolio projects', description: 'Create 3-5 projects to showcase skills' }
        ],
        isActive: true
      }
    });

    // Create test suggestion rule
    await prisma.suggestionRule.create({
      data: {
        careerPathId: testCareerPath.id,
        questionKey: 'top_skills',
        answerValue: 'technical_programming',
        weight: 5
      }
    });
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.assessmentResponse.deleteMany({
      where: { assessmentId: testAssessmentId }
    });
    await prisma.suggestionRule.deleteMany({});
    await prisma.assessment.deleteMany({
      where: { userId: testUserId }
    });
    await prisma.careerPath.deleteMany({});
    await prisma.user.deleteMany({
      where: { id: testUserId }
    });
  });

  describe('Assessment Results API', () => {
    it('should return comprehensive assessment results', async () => {
      const request = new NextRequest(`http://localhost/api/assessment/results/${testAssessmentId}`);
      const response = await GET(request, { params: { id: testAssessmentId } });
      
      expect(response.status).toBe(200);
      
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data).toBeDefined();
      
      const results = data.data;
      
      // Check assessment data
      expect(results.assessment.id).toBe(testAssessmentId);
      expect(results.assessment.status).toBe('COMPLETED');
      
      // Check insights
      expect(results.insights).toBeDefined();
      expect(results.insights.scores).toBeDefined();
      expect(results.insights.scores.readinessScore).toBeGreaterThan(0);
      expect(results.insights.topSkills).toContain('technical_programming');
      expect(results.insights.primaryMotivation).toBe('better_work_life_balance');
      
      // Check career suggestions
      expect(results.careerSuggestions).toBeDefined();
      expect(Array.isArray(results.careerSuggestions)).toBe(true);
      
      if (results.careerSuggestions.length > 0) {
        const suggestion = results.careerSuggestions[0];
        expect(suggestion.careerPath).toBeDefined();
        expect(suggestion.score).toBeGreaterThan(0);
        expect(suggestion.matchReason).toBeDefined();
      }
    });

    it('should return 404 for non-existent assessment', async () => {
      const request = new NextRequest('http://localhost/api/assessment/results/non-existent-id');
      const response = await GET(request, { params: { id: 'non-existent-id' } });
      
      expect(response.status).toBe(404);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Assessment not found');
    });

    it('should return 400 for incomplete assessment', async () => {
      // Create incomplete assessment
      const incompleteAssessment = await prisma.assessment.create({
        data: {
          userId: testUserId,
          status: 'IN_PROGRESS',
          currentStep: 2,
        }
      });

      const request = new NextRequest(`http://localhost/api/assessment/results/${incompleteAssessment.id}`);
      const response = await GET(request, { params: { id: incompleteAssessment.id } });
      
      expect(response.status).toBe(400);
      
      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Assessment not completed');

      // Clean up
      await prisma.assessment.delete({
        where: { id: incompleteAssessment.id }
      });
    });
  });

  describe('Assessment Insights Generation', () => {
    it('should generate correct insights from assessment responses', () => {
      const responses = {
        financial_comfort: 4,
        confidence_level: 3,
        support_system: 4,
        risk_tolerance: 3,
        top_skills: ['technical_programming', 'problem_solving'],
        career_change_motivation: 'better_work_life_balance',
        biggest_obstacles: ['financial_concerns'],
        transition_timeline: 'medium_term'
      };

      const insights = generateAssessmentInsights(responses);
      
      expect(insights.scores.readinessScore).toBeGreaterThan(0);
      expect(insights.scores.readinessScore).toBeLessThanOrEqual(100);
      expect(insights.scores.financialReadiness).toBe(4);
      expect(insights.scores.riskTolerance).toBe(3);
      expect(insights.topSkills).toContain('technical_programming');
      expect(insights.primaryMotivation).toBe('better_work_life_balance');
      expect(insights.biggestObstacles).toContain('financial_concerns');
      expect(insights.recommendedTimeline).toBeDefined();
      expect(insights.keyRecommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Career Path Suggestions', () => {
    it('should generate career suggestions based on assessment', async () => {
      const suggestions = await getCareerPathSuggestions(testAssessmentId);
      
      expect(Array.isArray(suggestions)).toBe(true);
      
      if (suggestions.length > 0) {
        const suggestion = suggestions[0];
        expect(suggestion.careerPath).toBeDefined();
        expect(suggestion.careerPath.name).toBeDefined();
        expect(suggestion.score).toBeGreaterThan(0);
        expect(suggestion.matchReason).toBeDefined();
        expect(suggestion.skillAlignment).toBeGreaterThanOrEqual(0);
      }
    });

    it('should return suggestions sorted by score', async () => {
      const suggestions = await getCareerPathSuggestions(testAssessmentId);
      
      if (suggestions.length > 1) {
        for (let i = 0; i < suggestions.length - 1; i++) {
          expect(suggestions[i].score).toBeGreaterThanOrEqual(suggestions[i + 1].score);
        }
      }
    });
  });
});
