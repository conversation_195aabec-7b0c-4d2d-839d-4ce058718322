/**
 * Basic Test Suite
 * Verifies that the testing environment is properly configured
 * Now includes real database connection testing
 */

import { PrismaClient } from '@prisma/client';

describe('Basic Testing Environment', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should support TypeScript', () => {
    const testObject: { name: string; value: number } = {
      name: 'test',
      value: 42
    };

    expect(testObject.name).toBe('test');
    expect(testObject.value).toBe(42);
  });

  it('should support async operations', async () => {
    const asyncFunction = async (): Promise<string> => {
      return new Promise((resolve) => {
        setTimeout(() => resolve('async result'), 10);
      });
    };

    const result = await asyncFunction();
    expect(result).toBe('async result');
  });

  it('should have environment variables', () => {
    expect(process.env.NODE_ENV).toBeDefined();
    expect(process.env.DATABASE_URL).toBeDefined();
  });

  it('should connect to mock database', async () => {
    // Import the mocked prisma
    const prisma = require('@/lib/prisma').default;

    try {
      // Test mock database connection
      prisma.$connect.mockResolvedValue(undefined);
      await prisma.$connect();

      // Test a simple mock query
      prisma.$queryRaw.mockResolvedValue([{ test: 1 }]);
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      expect(result).toBeDefined();
      expect(result).toEqual([{ test: 1 }]);

      console.log('✅ Mock database connection successful');
    } catch (error) {
      console.error('❌ Mock database connection failed:', error);
      throw error;
    }
  });

  it('should be able to count users in mock database', async () => {
    // Import the mocked prisma
    const prisma = require('@/lib/prisma').default;

    try {
      // Mock user count
      prisma.user.count.mockResolvedValue(5);

      const userCount = await prisma.user.count();
      expect(typeof userCount).toBe('number');
      expect(userCount).toBeGreaterThanOrEqual(0);
      expect(userCount).toBe(5);

      console.log(`✅ Found ${userCount} users in mock database`);
    } catch (error) {
      console.error('❌ User count query failed:', error);
      throw error;
    }
  });
});
