/**
 * Basic Test Suite
 * Verifies that the testing environment is properly configured
 * Now includes real database connection testing
 */

import { PrismaClient } from '@prisma/client';

describe('Basic Testing Environment', () => {
  it('should run basic tests', () => {
    expect(1 + 1).toBe(2);
  });

  it('should support TypeScript', () => {
    const testObject: { name: string; value: number } = {
      name: 'test',
      value: 42
    };

    expect(testObject.name).toBe('test');
    expect(testObject.value).toBe(42);
  });

  it('should support async operations', async () => {
    const asyncFunction = async (): Promise<string> => {
      return new Promise((resolve) => {
        setTimeout(() => resolve('async result'), 10);
      });
    };

    const result = await asyncFunction();
    expect(result).toBe('async result');
  });

  it('should have environment variables', () => {
    expect(process.env.NODE_ENV).toBeDefined();
    expect(process.env.DATABASE_URL).toBeDefined();
  });

  it('should connect to real database', async () => {
    const prisma = new PrismaClient();

    try {
      // Test basic database connection
      await prisma.$connect();

      // Test a simple query
      const result = await prisma.$queryRaw`SELECT 1 as test`;
      expect(result).toBeDefined();

      console.log('✅ Real database connection successful');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  });

  it('should be able to count users in real database', async () => {
    const prisma = new PrismaClient();

    try {
      await prisma.$connect();

      const userCount = await prisma.user.count();
      expect(typeof userCount).toBe('number');
      expect(userCount).toBeGreaterThanOrEqual(0);

      console.log(`✅ Found ${userCount} users in database`);
    } catch (error) {
      console.error('❌ User count query failed:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  });
});
