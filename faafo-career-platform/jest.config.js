const nextJest = require('next/jest')

const createJestConfig = nextJest({
  // Provide the path to your Next.js app to load next.config.js and .env files
  dir: './',
})

// Add any custom config to be passed to Jest
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  setupFiles: ['<rootDir>/jest.polyfills.js'],
  testEnvironment: 'jsdom',
  moduleNameMapper: {
    // Mock Prisma first
    '^@/lib/prisma$': '<rootDir>/__mocks__/lib/prisma.js',
    // Handle specific components that exist in root directory (must come before general pattern)
    '^@/components/SignupForm$': '<rootDir>/components/SignupForm.tsx',
    '^@/components/LoginForm$': '<rootDir>/components/LoginForm.tsx',
    '^@/components/EmailVerificationBanner$': '<rootDir>/components/EmailVerificationBanner.tsx',
    '^@/components/SessionWrapper$': '<rootDir>/components/SessionWrapper.tsx',
    '^@/components/ErrorBoundary$': '<rootDir>/components/ErrorBoundary.tsx',
    '^@/components/CookieConsentBanner$': '<rootDir>/components/CookieConsentBanner.tsx',
    // Handle module aliases for src/components (default - must come after specific mappings)
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/app/(.*)$': '<rootDir>/src/app/$1',
    '^@/emails/(.*)$': '<rootDir>/src/emails/$1',
    '^@/types/(.*)$': '<rootDir>/src/types/$1',
    // Handle CSS imports (with CSS modules)
    '^.+\\.module\\.(css|sass|scss)$': 'identity-obj-proxy',
    // Handle CSS imports (without CSS modules)
    '^.+\\.(css|sass|scss)$': 'identity-obj-proxy',
    // Handle image imports
    '^.+\\.(png|jpg|jpeg|gif|webp|avif|ico|bmp|svg)$/i': '<rootDir>/__mocks__/fileMock.js',
  },
  testMatch: [
    '**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)',
    '**/*.(test|spec).(ts|tsx|js|jsx)'
  ],
  testPathIgnorePatterns: [
    '<rootDir>/node_modules/',
    '<rootDir>/__tests__/e2e/',  // Exclude Playwright tests
    '<rootDir>/__tests__/integration/security.test.ts',  // Exclude problematic integration tests
    '<rootDir>/__tests__/integration/api.test.ts',
    '<rootDir>/__tests__/api/',  // Exclude API tests that import route handlers
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{ts,tsx}',
    '!**/__tests__/**',
    '!**/node_modules/**',
    '!src/app/layout.tsx', // Exclude layout files
    '!src/app/global-error.tsx', // Exclude error boundaries
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 15000,
  // Handle ES modules
  extensionsToTreatAsEsm: ['.ts', '.tsx'],
  transform: {
    '^.+\\.(ts|tsx)$': ['ts-jest', {
      useESM: true,
      tsconfig: {
        jsx: 'react-jsx',
        module: 'esnext',
        target: 'es2020',
      },
    }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(jose|@auth|@next|next-auth|openid-client|uuid|@prisma)/)',
  ],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  testEnvironmentOptions: {
    customExportConditions: [''],
  },
}

// createJestConfig is exported this way to ensure that next/jest can load the Next.js config which is async
module.exports = createJestConfig(customJestConfig)