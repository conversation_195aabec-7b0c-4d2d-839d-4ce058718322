import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      pathname: '/',
      route: '/',
      query: {},
      asPath: '/',
    };
  },
  useSearchParams() {
    return new URLSearchParams();
  },
  usePathname() {
    return '/';
  },
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href, ...props }) => {
    return React.createElement('a', { href, ...props }, children);
  };
});

// Mock Next.js Image component
jest.mock('next/image', () => {
  return ({ src, alt, ...props }) => {
    return React.createElement('img', { src, alt, ...props });
  };
});

// Mock NextAuth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(() => ({
    data: null,
    status: 'unauthenticated',
  })),
  signIn: jest.fn(() => Promise.resolve({ ok: true })),
  signOut: jest.fn(),
  getSession: jest.fn(() => Promise.resolve({ user: { id: '1', email: '<EMAIL>' } })),
  SessionProvider: ({ children }) => children,
}));

// Mock NextAuth core
jest.mock('next-auth', () => ({
  default: jest.fn(),
  getServerSession: jest.fn(),
}));

// Mock NextAuth adapters
jest.mock('@auth/prisma-adapter', () => ({
  PrismaAdapter: jest.fn(),
}));

// Mock UUID
jest.mock('uuid', () => ({
  v4: jest.fn(() => 'test-uuid-1234'),
  v1: jest.fn(() => 'test-uuid-v1-1234'),
  validate: jest.fn(() => true),
}));

// Create comprehensive Prisma mock
const createMockModel = () => ({
  findUnique: jest.fn(),
  findMany: jest.fn(),
  findFirst: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  deleteMany: jest.fn(),
  count: jest.fn(),
  upsert: jest.fn(),
  createMany: jest.fn(),
  updateMany: jest.fn(),
  aggregate: jest.fn(),
  groupBy: jest.fn(),
});

const mockPrismaClient = {
  user: createMockModel(),
  assessment: createMockModel(),
  assessmentResponse: createMockModel(),
  learningResource: createMockModel(),
  careerPath: createMockModel(),
  careerPathResource: createMockModel(),
  profile: createMockModel(),
  forumPost: createMockModel(),
  forumReply: createMockModel(),
  userProgress: createMockModel(),
  userSkillProgress: createMockModel(),
  resourceRating: createMockModel(),
  skill: createMockModel(),
  userSkill: createMockModel(),
  industry: createMockModel(),
  notification: createMockModel(),
  userGoal: createMockModel(),
  achievement: createMockModel(),
  userAchievement: createMockModel(),
  postReaction: createMockModel(),
  moderatorAction: createMockModel(),
  report: createMockModel(),
  suggestionRule: createMockModel(),
  $connect: jest.fn().mockResolvedValue(undefined),
  $disconnect: jest.fn().mockResolvedValue(undefined),
  $transaction: jest.fn().mockImplementation((callback) => callback(mockPrismaClient)),
  $queryRaw: jest.fn(),
  $executeRaw: jest.fn(),
};

// Export to global for easy access in tests
global.mockPrisma = mockPrismaClient;

// Mock Prisma Client
jest.mock('@prisma/client', () => {
  return {
    PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient),
    Prisma: {
      PrismaClientKnownRequestError: class PrismaClientKnownRequestError extends Error {
        constructor(message, code, clientVersion) {
          super(message);
          this.code = code;
          this.clientVersion = clientVersion;
        }
      },
      PrismaClientUnknownRequestError: class PrismaClientUnknownRequestError extends Error {},
      PrismaClientRustPanicError: class PrismaClientRustPanicError extends Error {},
      PrismaClientInitializationError: class PrismaClientInitializationError extends Error {},
      PrismaClientValidationError: class PrismaClientValidationError extends Error {},
    },
  };
}, { virtual: true });

// Mock our Prisma lib
jest.mock('@/lib/prisma', () => ({
  default: mockPrismaClient,
  __esModule: true,
}));

// Global test utilities
global.React = require('react');